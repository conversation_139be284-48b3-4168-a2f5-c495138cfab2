#!/bin/bash

# API Startup Script
# This script runs database migrations and starts the API server

set -e

echo "🚀 Starting KB Tracker API..."

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
until npx prisma db push --accept-data-loss 2>/dev/null; do
  echo "Database not ready, waiting 5 seconds..."
  sleep 5
done

echo "✅ Database schema synchronized"

# Generate Prisma client (in case it's not already generated)
echo "🔧 Generating Prisma client..."
npx prisma generate

echo "✅ Prisma client generated"

# Start the API server
echo "🌟 Starting API server..."
exec node dist/src/main.js
